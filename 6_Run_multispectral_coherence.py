# File: run_multispectral_coherence.py

import time
import os
import numpy as np
import dask.array as da
import importlib.util
import sys

# Import local io module explicitly to avoid shadowing stdlib io
spec = importlib.util.spec_from_file_location("segy_io", os.path.join(os.path.dirname(__file__), "io.py"))
segy_io = importlib.util.module_from_spec(spec)
spec.loader.exec_module(segy_io)

import util
from Edge_dgeo import EdgeDetection
from SignalProcess import SignalProcess # Still needed for Edge_dgeo dependencies
from fborga_2d_3d import fborga_3d # Import the 3D Borga function
import tkinter as tk
from tkinter import filedialog, simpledialog, messagebox, ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.colors as colors
import segyio

def get_user_inputs():
    """Prompts the user for input file, output file, and processing parameters."""
    root = tk.Tk()
    root.withdraw() # We don't need the main window, only dialogs

    # --- File Selection ---
    input_segy_path = filedialog.askopenfilename(
        title="Select Input SEGY File",
        filetypes=[("SEGY files", "*.sgy *.segy"), ("All files", "*.*")],
        parent=root
    )
    if not input_segy_path:
        messagebox.showerror("Error", "Input file selection cancelled. Exiting.", parent=root)
        root.destroy()
        return None

    # Make output SEGY optional
    save_output = messagebox.askyesno("Save Output", "Do you want to save the output to a SEGY file?", parent=root)
    output_segy_path = None
    if save_output:
        output_segy_path = filedialog.asksaveasfilename(
            title="Specify Output SEGY File Path (Optional)",
            defaultextension=".sgy",
            filetypes=[("SEGY files", "*.sgy *.segy"), ("All files", "*.*")],
            parent=root
        )
        if not output_segy_path:  # User cancelled the save dialog
            save_output = False

    # --- Borga Decomposition Parameters ---
    default_target_freqs = "5, 10, 15, 20, 25, 30"
    default_fwidth = "10"
    default_finc = "15"
    default_kernel = "5, 5, 11"

    while True:
        target_frequencies_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter target frequencies (Hz, comma-separated):\n(e.g., {default_target_freqs})",
            initialvalue=default_target_freqs,
            parent=root
        )
        if target_frequencies_str is None: messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root); root.destroy(); return None
        try:
            target_frequencies = [float(f.strip()) for f in target_frequencies_str.split(',')]
            if not target_frequencies: raise ValueError("List cannot be empty.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for target frequencies: {e}\nPlease use comma-separated numbers (e.g., 5,10,15).", parent=root)

    while True:
        fwidth_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter Gaussian window width (fwidth, Hz):\n(e.g., {default_fwidth})",
            initialvalue=default_fwidth,
            parent=root
        )
        if fwidth_str is None: messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root); root.destroy(); return None
        try:
            fwidth = float(fwidth_str)
            if fwidth <= 0: raise ValueError("fwidth must be positive.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for fwidth: {e}\nPlease enter a positive number (e.g., 5.0).", parent=root)

    while True:
        finc_str = simpledialog.askstring(
            "Borga Parameters",
            f"Enter frequency increment (finc, Hz):\n(e.g., {default_finc})",
            initialvalue=default_finc,
            parent=root
        )
        if finc_str is None: messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root); root.destroy(); return None
        try:
            finc = float(finc_str)
            if finc <= 0: raise ValueError("finc must be positive.")
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for finc: {e}\nPlease enter a positive number (e.g., 1.0).", parent=root)

    # --- Coherence Calculation Parameters ---
    while True:
        kernel_str = simpledialog.askstring(
            "Coherence Parameters",
            f"Enter kernel shape (inlines,xlines,time_samples, comma-separated):\n(e.g., {default_kernel})",
            initialvalue=default_kernel,
            parent=root
        )
        if kernel_str is None: messagebox.showinfo("Cancelled", "Operation cancelled by user.", parent=root); root.destroy(); return None
        try:
            kernel_dims = [int(k.strip()) for k in kernel_str.split(',')]
            if len(kernel_dims) != 3:
                raise ValueError("Kernel must have 3 dimensions.")
            if not all(d > 0 for d in kernel_dims):
                raise ValueError("Kernel dimensions must be positive integers.")
            kernel = tuple(kernel_dims)
            break
        except ValueError as e:
            messagebox.showerror("Input Error", f"Invalid format for kernel: {e}\nPlease use 3 comma-separated positive integers (e.g., 3,3,11).", parent=root)
    
    root.destroy() # Clean up the hidden root window

    return {
        "input_segy_path": input_segy_path,
        "output_segy_path": output_segy_path,
        "save_output": save_output,
        "target_frequencies": target_frequencies,
        "fwidth": fwidth,
        "finc": finc,
        "kernel": kernel
    }


def main():
    """
    Main workflow for computing multispectral coherence using Borga decomposition.
    """
    temp_root_for_dialogs = None  # Ensure variable is always defined
    # --- 1. GET USER INPUTS ---
    params = get_user_inputs()
    if not params:
        print("Process cancelled due to missing inputs or user cancellation.")
        return

    input_segy_path = params["input_segy_path"]
    output_segy_path = params["output_segy_path"]
    target_frequencies = params["target_frequencies"]
    fwidth = params["fwidth"]
    finc = params["finc"]
    kernel = params["kernel"]

    print("--- Starting Multispectral Coherence Workflow with Borga Decomposition ---")

    # --- 2. LOAD SEISMIC DATA using io.py ---
    # NOTE: The provided io.py's segy_read writes to HDF5. We need to adapt the workflow.
    # Let's assume a simplified loader that returns the data and spec directly for this example.
    # If using the original io.py, you would read from the HDF5 file here.
    
    print(f"Loading seismic data from: {input_segy_path}")
    try:
        seismic_data_numpy, metadata = util.load_seismic_data(input_segy_path)
        dt = metadata['dt']
        
        if metadata['type'] == 'numpy' and dt is None:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
            dt_str = simpledialog.askstring("Input Sample Interval", 
                                            f"Input file '{os.path.basename(input_segy_path)}' is NumPy type and lacks sample interval.\nPlease enter sample interval (dt) in seconds (e.g., 0.002 or 0.004):",
                                            parent=temp_root_for_dialogs)
            if dt_str:
                try:
                    dt = float(dt_str)
                    metadata['dt'] = dt # Update metadata with user-provided dt
                except ValueError:
                    messagebox.showerror("Error", "Invalid dt value. Exiting.", parent=temp_root_for_dialogs)
                    if temp_root_for_dialogs:
                        try:
                            temp_root_for_dialogs.destroy()
                        except tk.TclError:
                            pass  # Already destroyed, ignore
                    return
            else: # User cancelled or entered nothing
                messagebox.showerror("Error", "Sample interval (dt) is required for .npy files for processing. Exiting.", parent=temp_root_for_dialogs)
                if temp_root_for_dialogs:
                    try:
                        temp_root_for_dialogs.destroy()
                    except tk.TclError:
                        pass  # Already destroyed, ignore
                return
        
        if seismic_data_numpy.ndim == 2 and metadata['geometry'] == '2D':
            seismic_data_numpy = seismic_data_numpy.reshape((1, seismic_data_numpy.shape[0], seismic_data_numpy.shape[1]))
            print(f"Input data was 2D. Reshaped from {metadata['shape']} to {seismic_data_numpy.shape} for 3D workflow.")
        elif seismic_data_numpy.ndim != 3:
            err_msg = f"Loaded data has {seismic_data_numpy.ndim} dimensions ({seismic_data_numpy.shape}). The workflow expects 3D data (Inline, Crossline, Time)."
            # Use a temporary root for this messagebox if get_user_inputs's root is gone
            if not temp_root_for_dialogs: 
                temp_root_for_dialogs = tk.Tk()
                temp_root_for_dialogs.withdraw()
            messagebox.showerror("Data Shape Error", err_msg, parent=temp_root_for_dialogs)
            if temp_root_for_dialogs:
                try:
                    temp_root_for_dialogs.destroy()
                except tk.TclError:
                    pass  # Already destroyed, ignore
            return

        seismic_data_il_xl_t = seismic_data_numpy 
        template_segy_path = metadata.get('template_path') # Will be None for .npy files

        print(f"Successfully loaded: type='{metadata['type']}', geometry='{metadata['geometry']}', shape={seismic_data_il_xl_t.shape}, dt={dt}s")

    except FileNotFoundError as e:
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("File Not Found", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except ValueError as e: # Covers unsupported file type
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("File Load Error", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except IOError as e: # Covers SEGY reading issues
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("SEGY Read Error", str(e), parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    except Exception as e: # Catch-all for other unexpected loading errors
        if not temp_root_for_dialogs:
            temp_root_for_dialogs = tk.Tk()
            temp_root_for_dialogs.withdraw()
        messagebox.showerror("Loading Error", f"An unexpected error occurred during loading: {e}", parent=temp_root_for_dialogs)
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore
        return
    finally:
        if temp_root_for_dialogs:
            try:
                temp_root_for_dialogs.destroy()
            except tk.TclError:
                pass  # Already destroyed, ignore

    # Integration with robust loader as per add_custom_load_segy.md:
    # Use time_axis from metadata if present (SEGY), else fallback to np.arange(...)*dt (NumPy)
    time_axis = metadata['time_axis'] if metadata.get('time_axis') is not None else np.arange(seismic_data_il_xl_t.shape[2]) * dt
    print(f"Data loaded. Shape (IL, XL, T): {seismic_data_il_xl_t.shape}, dt: {dt:.4f}s")


    # --- 3. GENERATE SPECTRAL VOICES using fborga_3d ---
    print(f"Generating spectral voices for target frequencies: {target_frequencies} Hz...")
    start_time = time.time()
    
    # fborga_3d expects (time, inline, xline). We must transpose.
    seismic_t_il_xl = np.transpose(seismic_data_il_xl_t, (2, 0, 1))
    
    # Run the Borga transform
    tvs_4d, fout, _ = fborga_3d(
        seismic_volume=seismic_t_il_xl,
        t=time_axis,
        fwidth=fwidth,
        finc=finc,
        target_freqs=target_frequencies
    )
    # Output shape of tvs_4d is (time, freq, inline, xline)
    
    end_time = time.time()
    print(f"Borga decomposition complete in {end_time - start_time:.2f} seconds.")
    print(f"Generated {tvs_4d.shape[1]} voices at frequencies: {np.round(fout, 1)} Hz")

    # --- 4. QUALITY CONTROL: VISUALIZE SPECTRAL VOICES ---
    print("Displaying spectral voices for quality control...")

    def show_spectral_voices_qc():
        """Display spectral voices and get user approval to continue."""
        # Create QC window
        qc_window = tk.Tk()
        qc_window.title("Spectral Voices Quality Control")

        # Create main frame
        main_frame = ttk.Frame(qc_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Add instruction label
        instruction_label = ttk.Label(main_frame,
                                    text="Review the spectral voices below. Click 'Continue' to proceed with coherence calculation or 'Cancel' to exit.",
                                    font=('Arial', 10, 'bold'))
        instruction_label.pack(pady=(0, 10))

        # Calculate subplot layout
        n_voices = tvs_4d.shape[1]
        n_cols = min(3, n_voices)  # Max 3 columns
        n_rows = int(np.ceil((n_voices + 1) / n_cols))  # +1 for original seismic

        # Create figure with subplots
        fig_width = min(16, 5 * n_cols)
        fig_height = min(12, 4 * n_rows)
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(fig_width, fig_height))

        # Ensure axes is always 2D array for consistent indexing
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        if n_cols == 1:
            axes = axes.reshape(-1, 1)

        # Get middle inline for visualization
        mid_il = seismic_data_il_xl_t.shape[0] // 2

        # Plot original seismic in first subplot
        ax = axes[0, 0]
        vmin_orig, vmax_orig = np.percentile(seismic_data_il_xl_t[mid_il, :, :], [2, 98])
        im = ax.imshow(seismic_data_il_xl_t[mid_il, :, :].T,
                      aspect='auto', cmap='seismic',
                      vmin=vmin_orig, vmax=vmax_orig,
                      extent=[0, seismic_data_il_xl_t.shape[1],
                             seismic_data_il_xl_t.shape[2]*dt, 0])
        ax.set_title(f'Original Seismic\n(IL: {mid_il})', fontsize=10, fontweight='bold')
        ax.set_xlabel('Crossline')
        ax.set_ylabel('Time (s)')
        plt.colorbar(im, ax=ax, label='Amplitude', shrink=0.8)

        # Plot each spectral voice
        plot_idx = 1
        for freq_idx in range(n_voices):
            row = plot_idx // n_cols
            col = plot_idx % n_cols

            if row < n_rows and col < n_cols:
                ax = axes[row, col]

                # Get the spectral voice data (freq, inline, xline, time) -> (inline, xline, time)
                voice_data = tvs_4d[:, freq_idx, :, :].T  # Transpose to (inline, xline, time)
                voice_slice = voice_data[mid_il, :, :]  # Get same inline slice

                # Use magnitude for complex data
                if np.iscomplexobj(voice_slice):
                    voice_slice = np.abs(voice_slice)

                # Calculate percentile-based scaling for each voice
                vmin_voice, vmax_voice = np.percentile(voice_slice, [2, 98])

                im = ax.imshow(voice_slice.T,
                              aspect='auto', cmap='viridis',
                              vmin=vmin_voice, vmax=vmax_voice,
                              extent=[0, voice_data.shape[1],
                                     voice_data.shape[2]*dt, 0])
                ax.set_title(f'Spectral Voice\n{fout[freq_idx]:.1f} Hz', fontsize=10, fontweight='bold')
                ax.set_xlabel('Crossline')
                ax.set_ylabel('Time (s)')
                plt.colorbar(im, ax=ax, label='Magnitude', shrink=0.8)

            plot_idx += 1

        # Hide unused subplots
        total_plots = n_voices + 1
        for idx in range(total_plots, n_rows * n_cols):
            row = idx // n_cols
            col = idx % n_cols
            if row < n_rows and col < n_cols:
                axes[row, col].set_visible(False)

        plt.tight_layout()

        # Create frame for the plot
        plot_frame = ttk.Frame(main_frame)
        plot_frame.pack(fill=tk.BOTH, expand=True)

        # Embed the figure in the tkinter window
        canvas = FigureCanvasTkAgg(fig, master=plot_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Create button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # Variable to store user decision
        user_decision = {'continue': False}

        def on_continue():
            user_decision['continue'] = True
            qc_window.quit()
            qc_window.destroy()

        def on_cancel():
            user_decision['continue'] = False
            qc_window.quit()
            qc_window.destroy()

        # Add buttons
        continue_button = ttk.Button(button_frame, text="Continue with Coherence Calculation",
                                   command=on_continue)
        continue_button.pack(side=tk.RIGHT, padx=(5, 0))

        cancel_button = ttk.Button(button_frame, text="Cancel", command=on_cancel)
        cancel_button.pack(side=tk.RIGHT)

        # Center the window
        window_width = min(1400, int(qc_window.winfo_screenwidth() * 0.9))
        window_height = min(900, int(qc_window.winfo_screenheight() * 0.8))
        screen_width = qc_window.winfo_screenwidth()
        screen_height = qc_window.winfo_screenheight()
        x = (screen_width // 2) - (window_width // 2)
        y = (screen_height // 2) - (window_height // 2)
        qc_window.geometry(f'{window_width}x{window_height}+{x}+{y}')

        # Start the event loop
        qc_window.mainloop()

        return user_decision['continue']

    # Show QC window and get user decision
    if not show_spectral_voices_qc():
        print("Quality control check failed or cancelled by user. Exiting workflow.")
        return

    print("Quality control approved. Proceeding with coherence calculation...")

    # --- 5. PREPARE VOICES FOR COHERENCE CALCULATION ---
    # Our coherence function expects a list of Dask arrays, each shaped (inline, xline, time)

    # Transpose the 4D output to (freq, inline, xline, time)
    tvs_4d_transposed = np.transpose(tvs_4d, (1, 2, 3, 0))

    spectral_voices = []
    for i in range(tvs_4d_transposed.shape[0]):
        # Extract one voice (shape: inline, xline, time)
        voice_3d = tvs_4d_transposed[i, :, :, :]
        # Convert to Dask array for our function
        spectral_voices.append(da.from_array(voice_3d, chunks='auto'))

    print(f"Prepared {len(spectral_voices)} voices for coherence calculation.")

    # --- 6. COMPUTE MULTISPECTRAL COHERENCE ---
    print("Computing multispectral eigenstructure coherence...")
    start_time = time.time()

    edge_detector = EdgeDetection()

    # Call our function with the list of Dask arrays
    coherence_result_dask = edge_detector.multispectral_eig(
        spectral_voices,
        kernel=kernel
    )

    # Trigger the Dask computation
    coherence_result_numpy = coherence_result_dask.compute()

    end_time = time.time()
    print(f"Coherence computation finished in {end_time - start_time:.2f} seconds.")
    print(f"Result shape: {coherence_result_numpy.shape}")

    # --- 7. SAVE THE RESULT ---
    if params["save_output"] and output_segy_path: # output_segy_path is the user-specified path
        print(f"Preparing to save result to: {output_segy_path}")
        
        # template_segy_path is from metadata (None if input was .npy)
        # dt is also from metadata (potentially user-inputted for .npy)

        # Create a temporary root for save-related dialogs if needed
        save_dialog_root = None 

        try:
            if metadata['type'] == 'segy' and template_segy_path:
                print(f"Input was SEGY. Saving result as SEGY to: {output_segy_path}")
                segy_io.segy_write(coherence_result_numpy, template_segy_path, output_segy_path)
                print(f"Successfully saved SEGY file: {output_segy_path}")
            
            elif metadata['type'] == 'numpy':
                base, ext = os.path.splitext(output_segy_path)
                # Standardize output for NumPy input to be .npy
                output_path_npy = base + "_coherence.npy"
                
                if ext.lower() in ['.sgy', '.segy']:
                    save_dialog_root = tk.Tk(); save_dialog_root.withdraw()
                    warn_msg = f"Input was NumPy. Output will be saved in NumPy format (.npy) as '{output_path_npy}', not as the specified SEGY path '{output_segy_path}'."
                    print(f"WARNING: {warn_msg}")
                    messagebox.showwarning("Save Format Note", warn_msg, parent=save_dialog_root)
                
                print(f"Input was NumPy. Saving result as NumPy to: {output_path_npy}")
                np.save(output_path_npy, coherence_result_numpy)
                print(f"Successfully saved NumPy file: {output_path_npy}")
            else:
                save_dialog_root = tk.Tk(); save_dialog_root.withdraw()
                err_msg = f"Unknown input type '{metadata['type']}'. Cannot determine save format. Output not saved."
                print(f"Warning: {err_msg}")
                messagebox.showwarning("Save Warning", err_msg, parent=save_dialog_root)
        except Exception as e:
            if not save_dialog_root: save_dialog_root = tk.Tk(); save_dialog_root.withdraw()
            save_err_msg = f"Error during saving to '{output_segy_path}': {e}"
            print(save_err_msg)
            messagebox.showerror("Save Error", save_err_msg, parent=save_dialog_root)
        finally:
            if save_dialog_root: 
                save_dialog_root.destroy()
    
    # --- 8. PLOT RESULTS ---
    print("Displaying results...")
    
    # Create a tkinter window for plotting
    plot_window = tk.Tk()
    plot_window.title("Seismic and Coherence Comparison")
    
    # Create a frame for the plots
    frame = ttk.Frame(plot_window)
    frame.pack(fill=tk.BOTH, expand=True)
    
    # Create a figure with two subplots side by side
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # Plot original seismic (middle inline)
    mid_il = seismic_data_il_xl_t.shape[0] // 2
    vmin, vmax = np.percentile(seismic_data_il_xl_t, [2, 98])
    im1 = ax1.imshow(seismic_data_il_xl_t[mid_il, :, :].T, 
                    aspect='auto', cmap='seismic',
                    vmin=vmin, vmax=vmax,
                    extent=[0, seismic_data_il_xl_t.shape[1], 
                            seismic_data_il_xl_t.shape[2]*dt, 0])
    ax1.set_title(f'Original Seismic (IL: {mid_il})')
    ax1.set_xlabel('Crossline')
    ax1.set_ylabel('Time (s)')
    plt.colorbar(im1, ax=ax1, label='Amplitude')
    
    # Plot coherence (same inline)
    vmin, vmax = 0, 1  # Coherence ranges from 0 to 1
    im2 = ax2.imshow(coherence_result_numpy[mid_il, :, :].T, 
                    aspect='auto', cmap='viridis',
                    vmin=vmin, vmax=vmax,
                    extent=[0, coherence_result_numpy.shape[1], 
                            coherence_result_numpy.shape[2]*dt, 0])
    ax2.set_title('Multispectral Coherence')
    ax2.set_xlabel('Crossline')
    ax2.set_ylabel('Time (s)')
    plt.colorbar(im2, ax=ax2, label='Coherence')
    
    # Adjust layout and display
    plt.tight_layout()
    
    # Embed the figure in the tkinter window
    canvas = FigureCanvasTkAgg(fig, master=frame)
    canvas.draw()
    canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
    
    # Add a close button
    close_button = ttk.Button(plot_window, text="Close", command=plot_window.destroy)
    close_button.pack(pady=10)
    
    # Center the window
    window_width = 1200
    window_height = 800
    screen_width = plot_window.winfo_screenwidth()
    screen_height = plot_window.winfo_screenheight()
    x = (screen_width // 2) - (window_width // 2)
    y = (screen_height // 2) - (window_height // 2)
    plot_window.geometry(f'{window_width}x{window_height}+{x}+{y}')
    
    print("--- Workflow Complete ---")
    
    # Start the tkinter main loop for the plot window
    plot_window.mainloop()


if __name__ == "__main__":
    # Wrap the call to main() in a block to avoid issues with Dask multiprocessing on some platforms
    main()